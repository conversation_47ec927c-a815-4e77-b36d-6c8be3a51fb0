import sbt.complete.DefaultParsers.spaceDelimited

import java.io.ByteArrayInputStream
import scala.language.postfixOps
import scala.sys.process._
import scala.util.{Failure, Success, Try}

lazy val kubernetesDeploymentFolder: String = "deployment/kubernetes"

def getKubernetesClusterContext(clusterContext: String): Option[String] = {
  val hosts: String = s"host api.live.$clusterContext.ndp.kops.cloud" !!
  val pattern = """is an alias for api\.(.*)\.""".r
  pattern
    .findAllIn(hosts)
    .matchData
    .toSeq
    .headOption
    .map(_.group(1))
    .asInstanceOf[Option[String]]
}

lazy val deploy = inputKey[Unit]("Deploys to Kubernetes cluster")
deploy := {
  val args: Seq[String] = spaceDelimited("<arg>").parsed
  lazy val clusterContext = args.head
  lazy val namespace = args(1)
  lazy val buildNumber = args(2)

  lazy val kubernetesFiles: List[String] = List(
    s"$kubernetesDeploymentFolder/deployment.yml",
    s"$kubernetesDeploymentFolder/service.yml",
    s"$kubernetesDeploymentFolder/$clusterContext/configmap-$namespace.yml",
    s"$kubernetesDeploymentFolder/$clusterContext/ingress-$namespace.yml"
  )

  def getConfig(file: String): Option[String] = {
    val source = scala.io.Source.fromFile(file)
    val config = Try(source.mkString).map(
      _.replaceAll("\\{\\{BUILD_NUMBER}}", buildNumber)
        .replaceAll("\\{\\{NAMESPACE}}", namespace)
    )
    source.close()
    config match {
      case Success(result) => Some(result)
      case Failure(error) =>
        println(s"error: $error")
        streams.value.log.error(
          s"Error reading config file $file: ${error.getMessage}"
        )
        sys.exit(1)
        None
    }
  }

  val configs = (for {
    file <- kubernetesFiles
    config <- getConfig(file)
  } yield config).mkString("\n---\n")
  println(configs)

  val context: Option[String] = getKubernetesClusterContext(clusterContext)
  context match {
    case Some(kubernetesContext) =>
      streams.value.log.info(
        s"kubectl --context=$kubernetesContext apply --record -f -" #< new ByteArrayInputStream(
          configs.getBytes()
        ) !!
      )
      streams.value.log.info(
        s"kubectl --context=$kubernetesContext rollout status deployment match-manager --namespace $namespace" !!
      )
    case None => sys.exit(1)
  }
}

lazy val rollback = inputKey[Unit]("Rollback previous deployment")
rollback := {
  val args: Seq[String] = spaceDelimited("<arg>").parsed
  lazy val clusterContext = args.head
  lazy val namespace = args(1)

  val context: Option[String] = getKubernetesClusterContext(clusterContext)
  context match {
    case Some(kubernetesContext) =>
      streams.value.log.info(
        s"kubectl --context=$kubernetesContext rollout undo deployment match-manager --namespace $namespace" !!
      )
    case None => sys.exit(1)
  }
}
