# match-manager

## Introduction

Our company receives information on organizations via different systems. To combine those matching groups a system 
called DBT groups those organizations. This matching sometimes fails: organizations are grouped in the wrong group or 
are not added to an existing group. To correct any mismatches the Match Manager has been built.

The Match Manager can be found on

- [Production](https://match-manager-pro.persgroep.digital/) (with production data)
- [Acceptance](https://match-manager-acc.persgroep.digital/) (test data)


## Usage

With the Match Manager organizations can be searchd on all or a specified field. By clicking on their group id or 
original group id the current list of organizations can be shown. By clicking on any other column the "detail" page of 
the organization will be shown on which the organization can be detatched from the current group (a new group will be 
created for this company) or the organization can be added to another (existing) group.

## Technical stuff

### Id of an organization

The id of an organization is a compound key, based on the system that provided the data and the id of that system.

### Moving organizations to another group

When changing the group id we actually add a new row to the group (`CRA_ORGANIZATION_GROUP_OVERRIDES`) table. The 
organization (`CRA_ORGANIZATIONS`) table (which is actually a view) will then be updated when reading the data.

### Detaching organizations to a new group

To move an organization to a new group we simply generate a new id based on its id and timastamp of the moment we 
created the group.

### Stack

This application is built on

- [Http4s](https://http4s.org/) _stream http requests/responses_
- [Doobie](https://tpolecat.github.io/doobie/docs/01-Introduction.html) _compose database interaction_ 
- [Quill](https://getquill.io/) _compose database queries (compile time)_
- [ScalaTags](https://com-lihaoyi.github.io/scalatags/) _server side frontend components_

The underlying database is a [Snowflake](https://docs.snowflake.com/en/) databaes, this is a hosted SQL database.
Since there is no Snowflake implementation for quill the search queries are composed by using doobie fragments (this is 
done on runtime).

Http4s and Doobie make use of [Cats Effect 3](https://typelevel.org/cats-effect/).

### Running on local machine

To run on the local machine, an .env file needs to be added in the root of the project with values provided for:
```dotenv
DATABASE_USER= (your database user for the test environment)
DATABASE_PASSWORD= (your password for the test environment)
DATABASE_CONNECTION_STRING=**************************************************************************************************************
```
The application can then be started with `docker-compose up -d match-manager`.

### Security

To use this application access to our openvpn needs to be acquired.
