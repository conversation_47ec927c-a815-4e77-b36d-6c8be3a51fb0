package nl.dpgmedia.matchmanager.organization

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class PageSpec extends AnyFlatSpec with Matchers {

  "A Page" should " have an offset of 100 × the pages before the current ond" in {
    Page(1).offset shouldBe 0
    Page(10).offset shouldBe 9 * Page(10).itemsPerPage
  }

  it should "never have an offset lower than 0" in {
    Page(0).offset shouldBe 0
    Page(-10).offset shouldBe 0
  }

}
