package nl.dpgmedia.matchmanager.organization

import nl.dpgmedia.matchmanager.organization.QueryBuilder._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import doobie.implicits._

class SearchQueryBuilderSpec extends AnyFlatSpec with Matchers {
  behavior of "queryBuilder"

  val table: QueryBuilder = QueryBuilder(Table("someTable", List("firstKey", "secondKey")))

  it should "create a query for a single term in a single field" in {
    val queryPair = table.searchColumn("firstKey", "foo")

    queryPair.resultQuery.internals.sql shouldBe "select FIRST_KEY, SECOND_KEY from someTable WHERE ((FIRST_KEY ilike ?) ) "
    queryPair.resultQuery.internals.elements shouldBe List("%foo%")

    queryPair.countQuery.internals.sql shouldBe "select count(*) from someTable WHERE ((FIRST_KEY ilike ?) ) "
    queryPair.countQuery.internals.elements shouldBe List("%foo%")
  }

  it should "create a query for multiple terms in a single field" in {
    val queryPair = table.searchColumn("firstKey", "foo bar")

    queryPair.resultQuery.internals.sql shouldBe "select FIRST_KEY, SECOND_KEY from someTable WHERE ((FIRST_KEY ilike ?) ) AND ((FIRST_KEY ilike ?) ) "
    queryPair.resultQuery.internals.elements shouldBe List("%foo%", "%bar%")

    queryPair.countQuery.internals.sql shouldBe "select count(*) from someTable WHERE ((FIRST_KEY ilike ?) ) AND ((FIRST_KEY ilike ?) ) "
    queryPair.countQuery.internals.elements shouldBe List("%foo%", "%bar%")
  }

  it should "create a query for a single term in multiple fields" in {
    val queryPair = table.searchColumns(List("firstKey", "secondKey"), "foo")

    queryPair.resultQuery.internals.sql shouldBe "select FIRST_KEY, SECOND_KEY from someTable WHERE ((FIRST_KEY ilike ?) OR (SECOND_KEY ilike ?) ) "
    queryPair.resultQuery.internals.elements shouldBe List("%foo%", "%foo%")

    queryPair.countQuery.internals.sql shouldBe "select count(*) from someTable WHERE ((FIRST_KEY ilike ?) OR (SECOND_KEY ilike ?) ) "
    queryPair.countQuery.internals.elements shouldBe List("%foo%", "%foo%")
  }

  it should "create a query for multiple terms in multiple fields" in {
    val queryPair = table.searchColumns(List("firstKey", "secondKey"), "foo bar")

    queryPair.resultQuery.internals.sql shouldBe "select FIRST_KEY, SECOND_KEY from someTable WHERE ((FIRST_KEY ilike ?) OR (SECOND_KEY ilike ?) ) AND ((FIRST_KEY ilike ?) OR (SECOND_KEY ilike ?) ) "
    queryPair.resultQuery.internals.elements shouldBe List("%foo%", "%foo%", "%bar%", "%bar%")

    queryPair.countQuery.internals.sql shouldBe "select count(*) from someTable WHERE ((FIRST_KEY ilike ?) OR (SECOND_KEY ilike ?) ) AND ((FIRST_KEY ilike ?) OR (SECOND_KEY ilike ?) ) "
    queryPair.countQuery.internals.elements shouldBe List("%foo%", "%foo%", "%bar%", "%bar%")
  }

  behavior of "splitting searchvalues"

  it should "not create elements for empty strings in" in {
    val queryPair = table.searchColumns(List("firstKey"), "foo     bar")
    queryPair.resultQuery.internals.elements shouldBe List("%foo%", "%bar%")
  }

  behavior of "paginated query"

  it should "have an offset of 0 on the first page" in {
    val paginated = fr"select foo from bar".paginated(Page(1))
    paginated.internals.sql shouldBe "select foo from bar limit ? offset ?"
    paginated.internals.elements shouldBe List(100, 0)
  }

  it should "have an offset of 300 on the fourth page" in {
    val paginated = fr"select foo from bar".paginated(Page(4))
    paginated.internals.sql shouldBe "select foo from bar limit ? offset ?"
    paginated.internals.elements shouldBe List(100, 300)
  }

}
