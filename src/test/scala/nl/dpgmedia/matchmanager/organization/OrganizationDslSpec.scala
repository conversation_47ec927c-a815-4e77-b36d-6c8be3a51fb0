package nl.dpgmedia.matchmanager.organization

import cats.effect.IO
import cats.effect.testing.scalatest.AsyncIOSpec
import org.scalatest.flatspec.AsyncFlatSpec
import org.scalatest.matchers.should.Matchers

class OrganizationDslSpec extends AsyncFlatSpec with AsyncIOSpec with Matchers {

  val stubbedRepo: OrganizationRepository[IO] = new OrganizationRepository[IO] {
    override def getByGroup(groupId: String): IO[OrganizationResult] = ???

    override def search(searchQuery: String, field: String, page: Page): IO[OrganizationResult] = ???

    override def get(id: Organization.Id): IO[Option[Organization]] = ???

    override def moveOrganizationToExistingGroup(organizationId: Organization.Id, existingGroupId: String): IO[Int] = ???

    override def moveOrganizationToNewGroup(organizationId: Organization.Id): IO[Int] = ???
  }

  val dsl: OrganizationDsl[IO] = OrganizationDsl.impl[IO](stubbedRepo)

  behavior of "Grouping action"

  it should "raise an error when no action is provided" in {
    dsl.extractGroupingActionFromRequest(Map("groupId" -> "abcd")).attempt.asserting(_ shouldBe Left(GroupingAction.NoAction))
  }

  it should "raise an error when an unknown action is provided" in {
    dsl
      .extractGroupingActionFromRequest(Map("action" -> "unknown", "groupId" -> "abcd"))
      .attempt
      .asserting(_ shouldBe Left(GroupingAction.UnknownAction("unknown")))
  }

  "Attach action" should "be extracted from params when action is attach and an id is provided" in {
    dsl
      .extractGroupingActionFromRequest(Map("action" -> AttachToExisting.key, "groupId" -> "abcd"))
      .asserting(_ shouldBe AttachToExisting("abcd"))
  }

  "Detach action" should "be extracted from params when action detach is " in {
    dsl
      .extractGroupingActionFromRequest(Map("action" -> DetachFromGroup.key, "groupId" -> "abcd"))
      .asserting(_ shouldBe DetachFromGroup)
  }

}
