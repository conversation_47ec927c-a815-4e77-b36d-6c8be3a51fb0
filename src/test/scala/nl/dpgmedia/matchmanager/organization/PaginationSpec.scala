package nl.dpgmedia.matchmanager.organization

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class PaginationSpec extends AnyFlatSpec with Matchers {

  "The pagination" should "have no previous page when on the first page" in {
    Pagination(Page(1), 10).previousPage shouldBe None
  }

  it should "have a previous page when it is not on the first page" in {
    Pagination(Page(2), 210).previousPage shouldBe Some(Page(1))
    Pagination(Page(3), 210).previousPage shouldBe Some(Page(2))
  }

  it should "not have a next page when it is on the last page" in {
    Pagination(Page(3), 210).nextPage shouldBe None
  }

  it should "have a next page when it is not on the last page" in {
    Pagination(Page(2), 210).nextPage shouldBe Some(Page(3))
    Pagination(Page(1), 210).nextPage shouldBe Some(Page(2))
  }

  it should "calculate the last page" in {
    Pagination(Page(1), 210).lastPage shouldBe Page(3)

    Pagination(Page(1), 99).lastPage shouldBe Page(1)
    Pagination(Page(1), 100).lastPage shouldBe Page(1)
    Pagination(Page(1), 101).lastPage shouldBe Page(2)

    Pagination(Page(1), 199).lastPage shouldBe Page(2)
    Pagination(Page(1), 200).lastPage shouldBe Page(2)
    Pagination(Page(1), 201).lastPage shouldBe Page(3)
  }

  it should "have one page when there are no items" in {
    Pagination(Page(1), 0).lastPage shouldBe Page(1)
  }

}
