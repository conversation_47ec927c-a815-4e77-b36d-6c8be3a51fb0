package nl.dpgmedia.matchmanager

import org.ekrich.config.{Config, ConfigFactory}

object Configuration {
  private val config: Config = ConfigFactory.load()

  object Database {

    val user: String =
      config.getString("nl.dpgmedia.matchmanager.database.user")

    val password: String =
      config.getString("nl.dpgmedia.matchmanager.database.password")

    val connectionString: String =
      config.getString("nl.dpgmedia.matchmanager.database.connectionString")

  }

}
