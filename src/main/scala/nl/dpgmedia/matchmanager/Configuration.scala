package nl.dpgmedia.matchmanager

import org.ekrich.config.{Config, ConfigFactory}

object Configuration {
  private val config: Config = ConfigFactory.load()

  object Database {

    val user: String =
      config.getString("nl.dpgmedia.matchmanager.database.user")

    val privateKey: String =
      config.getString("nl.dpgmedia.matchmanager.database.privateKey")

    val privateKeyPassphrase: Option[String] =
      if (config.hasPath("nl.dpgmedia.matchmanager.database.privateKeyPassphrase"))
        Some(config.getString("nl.dpgmedia.matchmanager.database.privateKeyPassphrase"))
      else
        None

    val connectionString: String =
      config.getString("nl.dpgmedia.matchmanager.database.connectionString")

  }

}
