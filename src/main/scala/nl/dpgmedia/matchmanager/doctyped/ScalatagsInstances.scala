package nl.dpgmedia.matchmanager.doctyped

import _root_.scalatags.generic.Frag
import org.http4s.{Charset, DefaultCharset, EntityEncoder, MediaType}
import org.http4s.headers.`Content-Type`

object ScalatagsInstances {

  implicit def scalatagsEncoder[F[_], C <: Frag[_, String]](implicit
    charset: Charset = DefaultCharset
  ): EntityEncoder[F, C] =
    contentEncoder(MediaType.text.html)

  private def contentEncoder[F[_], C <: Frag[_, String]](
    mediaType: MediaType
  )(implicit charset: Charset): EntityEncoder[F, C] =
    EntityEncoder
      .stringEncoder[F]
      .contramap[C](content => "<!DOCTYPE html>" + content.render)
      .withContentType(`Content-Type`(mediaType, charset))

}
