package nl.dpgmedia.matchmanager.organization

case class Pagination(page: Page, total: Long) {

  def lastPage: Page =
    if (total <= page.itemsPerPage) Page(1)
    else Page(math.ceil(total.toDouble / page.itemsPerPage).toInt)

  def nextPage: Option[Page] =
    if (page.pageNumber * page.itemsPerPage >= total) None
    else Some(page.copy(page.pageNumber + 1))

  def previousPage: Option[Page] =
    if (page.pageNumber <= 1) None
    else Some(page.copy(page.pageNumber - 1))

}
