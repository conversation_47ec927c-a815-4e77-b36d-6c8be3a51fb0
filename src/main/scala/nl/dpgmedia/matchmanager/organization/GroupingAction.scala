package nl.dpgmedia.matchmanager.organization

sealed trait GroupingAction
case class AttachToExisting(existingGroupId: String) extends GroupingAction

object AttachToExisting extends GroupingAction.ParameterValue {
  override val key: String = "attach"
}

case object DetachFromGroup extends GroupingAction with GroupingAction.ParameterValue {
  override val key: String = "detach"
}

object GroupingAction {

  trait ParameterValue {
    val key: String
  }

  case object NoAction extends Throwable("No groupId action specified")
  case class UnknownAction(action: String) extends Throwable(s"Incorrect groupId action ($action) specified")
}
