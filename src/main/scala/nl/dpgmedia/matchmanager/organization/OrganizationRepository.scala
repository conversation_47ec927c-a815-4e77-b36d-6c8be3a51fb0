package nl.dpgmedia.matchmanager.organization

import cats.effect.Sync
import cats.syntax.all._
import doobie._
import doobie.implicits._
import io.getquill.{CompositeNamingStrategy2, SnakeCase, UpperCase}
import net.snowflake.client.jdbc.internal.amazonaws.util.Md5Utils
import org.polyvariant.doobiequill.DoobieContext

trait OrganizationRepository[F[_]] {
  def getByGroup(groupId: String): F[OrganizationResult]

  def search(searchQuery: String, field: String, page: Page): F[OrganizationResult]

  def get(id: Organization.Id): F[Option[Organization]]

  def moveOrganizationToExistingGroup(organizationId: Organization.Id, existingGroupId: String): F[Int]

  def moveOrganizationToNewGroup(organizationId: Organization.Id): F[Int]
}

object OrganizationRepository {

  def apply[F[_]: Sync](transactor: Transactor[F]): OrganizationRepository[F] = new OrganizationRepository[F] {

    override def getByGroup(groupId: String): F[OrganizationResult] =
      Queries.getByGroupId(groupId, Page(1)).transact(transactor)

    override def search(searchQuery: String, field: String, page: Page): F[OrganizationResult] =
      Queries.search(searchQuery, field, page).transact(transactor)

    override def get(id: Organization.Id): F[Option[Organization]] =
      Queries.get(id.sourceSystem, id.sourceSystemKey).transact(transactor)

    override def moveOrganizationToExistingGroup(organizationId: Organization.Id, existingGroupId: String): F[Int] = {
      val process = for {
        foundForGroup <- Queries.getByGroupId(existingGroupId, Page(1))
        _ <-
          if (foundForGroup.empty) new Throwable(s"GroupId $existingGroupId does not exist").raiseError[ConnectionIO, Int]
          else ().pure[ConnectionIO]
        update <- Queries.insertGroupOverride(GroupOverride(organizationId.sourceSystem, organizationId.sourceSystemKey, existingGroupId))
      } yield update

      process.transact(transactor)
    }

    override def moveOrganizationToNewGroup(organizationId: Organization.Id): F[Int] = {
      def process(): ConnectionIO[Int] = for {
        timestamp <- Queries.getTimestamp

        groupId = Md5Utils
          .computeMD5Hash((organizationId.sourceSystem + organizationId.sourceSystemKey + timestamp).getBytes)
          .map(b => String.format("%02x", b))
          .mkString

        foundForGroup <- Queries.getByGroupId(groupId, Page(1))
        _ <-
          if (foundForGroup.exists) new Throwable(s"GroupId $groupId already exists").raiseError[ConnectionIO, Int]
          else ().pure[ConnectionIO]
        result <- Queries.insertGroupOverride(groupId, organizationId.sourceSystem, organizationId.sourceSystemKey, timestamp)
      } yield result

      process().redeemWith(_ => process(), result => result.pure[ConnectionIO]).transact(transactor)
    }

  }

  object Queries {

    val context = new DoobieContext.MySQL(CompositeNamingStrategy2(SnakeCase, UpperCase))

    import context._

    // noinspection TypeAnnotation (When adding the annotation the query becomes dynamic and will not be created at compile time)
    implicit val organizationSchemaMeta =
      schemaMeta[Organization](""""SRC_MATCH_MANAGER"."CRA_ORGANIZATIONS"""")

    // noinspection TypeAnnotation (When adding the annotation the query becomes dynamic and will not be created at compile time)
    implicit val groupOverrideSchemaMeta =
      schemaMeta[GroupOverride](""""SRC_MATCH_MANAGER"."CRA_ORGANIZATION_GROUP_OVERRIDES"""")

    def getByGroupId(groupId: String, page: Page): ConnectionIO[OrganizationResult] = {
      val q = quote {
        query[Organization]
          .filter(_.groupId == lift(groupId))
      }

      for {
        resultCount <- run(q.size)
        results <- run(
          q
            .sortBy(o => (o.name, o.sourceSystem, o.sourceSystemKey))
            .drop(lift(page.offset))
            .take(lift(page.itemsPerPage))
        )
      } yield OrganizationResult(results, Pagination(page, resultCount))
    }

    def getByOriginalGroupId(groupId: String, page: Page): doobie.ConnectionIO[OrganizationResult] = {
      val q = quote {
        query[Organization]
          .filter(_.originalGroupId == lift(groupId))
      }

      for {
        resultCount <- run(q.size)
        results <- run(
          q
            .sortBy(o => (o.name, o.sourceSystem, o.sourceSystemKey))
            .drop(lift(page.offset))
            .take(lift(page.itemsPerPage))
        )
      } yield OrganizationResult(results, Pagination(page, resultCount))
    }

    def search(searchQuery: String, field: String, page: Page): ConnectionIO[OrganizationResult] = {

      import QueryBuilder._

      val builder = QueryBuilder(Table(""""SRC_MATCH_MANAGER"."CRA_ORGANIZATIONS"""", Organization.fieldNames.toList))
      val queries =
        if (!Organization.fieldNames.contains(field)) builder.searchAll(searchQuery)
        else builder.searchColumn(field, searchQuery)

      for {
        total <- queries.countQuery.query[Long].unique
        organizations <- queries.resultQuery
          .paginated(page)
          .queryWithLogHandler[Organization](LogHandler.jdkLogHandler)
          .to[List]
      } yield OrganizationResult(organizations, Pagination(page, total))
    }

    def get(sourceSystem: String, sourceSystemKey: String): ConnectionIO[Option[Organization]] = for {
      organization <- run(quote {
        query[Organization]
          .filter(_.sourceSystem == lift(sourceSystem))
          .filter(_.sourceSystemKey == lift(sourceSystemKey))
          .sortBy(o => (o.name, o.sourceSystem, o.sourceSystemKey))
          .take(1)
      })
    } yield organization.headOption

    def insertGroupOverride(groupOverride: GroupOverride): ConnectionIO[Index] =
      sql"""insert into "SRC_MATCH_MANAGER"."CRA_ORGANIZATION_GROUP_OVERRIDES" (GROUP_ID, SOURCE_SYSTEM, SOURCE_SYSTEM_KEY, CREATED_AT)
         values (${groupOverride.groupId}, ${groupOverride.sourceSystem}, ${groupOverride.sourceSystemKey}, current_timestamp()) """.update.run

    def insertGroupOverride(groupId: String, sourceSystem: String, sourceSystemKey: String, timestamp: String): ConnectionIO[Int] =
      sql"""insert into "SRC_MATCH_MANAGER"."CRA_ORGANIZATION_GROUP_OVERRIDES" (GROUP_ID, SOURCE_SYSTEM, SOURCE_SYSTEM_KEY, CREATED_AT)
         values ($groupId, $sourceSystem, $sourceSystemKey, $timestamp) """.update.run

    def getTimestamp: ConnectionIO[String] =
      sql"""select current_timestamp()""".query[String].unique

  }

}
