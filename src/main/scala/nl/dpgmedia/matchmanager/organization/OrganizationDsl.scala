package nl.dpgmedia.matchmanager.organization

import cats.effect.kernel.Async
import cats.syntax.all._

trait OrganizationDsl[F[_]] {
  def updateGroup(id: Organization.Id, groupingAction: GroupingAction): F[Unit]

  def extractGroupingActionFromRequest(params: Map[String, String]): F[GroupingAction]

  def search(query: String, field: String, page: Page): F[OrganizationResult]

  def getByGroupId(groupId: String): F[OrganizationResult]

  def get(id: Organization.Id): F[Option[Organization]]
}

object OrganizationDsl {

  def apply[F[_]](implicit ev: OrganizationDsl[F]): OrganizationDsl[F] = ev

  def impl[F[_]: Async](repository: OrganizationRepository[F]): OrganizationDsl[F] = new OrganizationDsl[F] {

    override def getByGroupId(groupId: String): F[OrganizationResult] = repository.getByGroup(groupId)

    override def get(id: Organization.Id): F[Option[Organization]] = repository.get(id)

    override def search(searchQuery: String, field: String, page: Page): F[OrganizationResult] = repository.search(searchQuery, field, page)

    override def updateGroup(organizationId: Organization.Id, groupingAction: GroupingAction): F[Unit] =
      groupingAction match {
        case AttachToExisting(existingGroupId) => repository.moveOrganizationToExistingGroup(organizationId, existingGroupId).void
        case DetachFromGroup                   => repository.moveOrganizationToNewGroup(organizationId).void
      }

    override def extractGroupingActionFromRequest(params: Map[String, String]): F[GroupingAction] =
      for {
        groupingAction <- params.get("action") match {
          case Some(AttachToExisting.key) =>
            params.get("groupId").map(AttachToExisting(_).pure[F]) getOrElse new Throwable("No target groupId specified")
              .raiseError[F, GroupingAction]
          case Some(DetachFromGroup.key) => DetachFromGroup.pure[F]
          case Some(value)               => GroupingAction.UnknownAction(value).raiseError[F, GroupingAction]
          case None                      => GroupingAction.NoAction.raiseError[F, GroupingAction]
        }
      } yield groupingAction

  }

}
