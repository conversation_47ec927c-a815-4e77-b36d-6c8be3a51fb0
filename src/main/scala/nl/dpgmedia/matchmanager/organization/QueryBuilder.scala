package nl.dpgmedia.matchmanager.organization

import doobie._
import doobie.Fragment._
import doobie.implicits._
import cats.syntax.foldable._
import doobie.util.fragments._
import io.getquill.{CompositeNamingStrategy2, SnakeCase, UpperCase}
import nl.dpgmedia.matchmanager.organization.QueryBuilder.{FragmentPair, Table}

case class QueryBuilder(table: Table) {

  private val strategy: CompositeNamingStrategy2[SnakeCase.type, UpperCase.type] = CompositeNamingStrategy2(SnakeCase, UpperCase)

  private def columnNameToFragment(columnName: String): Fragment = Fragment.const0(strategy.column(columnName))

  private def createFragmentPair(whereClause: Fragment): FragmentPair = {
    val columnNames: Fragment = table.columns.map(columnNameToFragment).intercalate(fr0", ")
    val tableName: Fragment = Fragment.const(table.name)

    FragmentPair(
      fr0"select " ++ columnNames ++ fr0" from " ++ tableName ++ whereClause,
      fr0"select count(*)" ++ fr0" from " ++ tableName ++ whereClause
    )
  }

  def searchColumn(columnName: String, searchValue: String): FragmentPair =
    searchColumns(List(columnName), searchValue)

  def searchColumns(columnNames: Seq[String], searchValue: String): FragmentPair = {
    val values = searchValue.split("""[\s]""").filter(_.nonEmpty).map(value => s"%$value%").toIndexedSeq
    val columns = columnNames.map(columnNameToFragment)
    createFragmentPair(whereAnd(values.map(value => or(columns.map(c => c ++ fr0" ilike $value"): _*)): _*))
  }

  def searchAll(searchValue: String): FragmentPair =
    searchColumns(table.columns, searchValue)

}

object QueryBuilder {

  implicit class PaginatedFragment(val fragment: Fragment) extends AnyVal {
    def paginated(page: Page): Fragment = fragment ++ fr0"limit ${page.itemsPerPage} offset ${page.offset}"
  }

  case class Table(name: String, columns: Seq[String])
  case class FragmentPair(resultQuery: Fragment, countQuery: Fragment)
}
