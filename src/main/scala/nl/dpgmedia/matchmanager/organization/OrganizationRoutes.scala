package nl.dpgmedia.matchmanager.organization

import cats.effect.Sync
import cats.implicits.catsSyntaxApplicativeError
import cats.syntax.applicative._
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.traverse._
import nl.dpgmedia.matchmanager.Layout
import nl.dpgmedia.matchmanager.doctyped.ScalatagsInstances._
import org.http4s.dsl.Http4sDsl
import org.http4s.headers.Location
import org.http4s.{HttpRoutes, Query, Response, ResponseCookie, Uri}
import scalatags.Text.TypedTag

object OrganizationRoutes {

  def apply[F[_]: Sync](organization: OrganizationDsl[F], layout: Layout[F]): HttpRoutes[F] = {
    val dsl = new Http4sDsl[F] {}
    import dsl._

    def errorResponse(msg: String): F[Response[F]] = for {
      page <- layout.Page(layout.Header, layout.ErrorMessage(msg)).render
      resp <- Ok(page)
    } yield resp

    object SearchQuery extends OptionalQueryParamDecoderMatcher[String]("q")
    object Field extends OptionalQueryParamDecoderMatcher[String]("f")
    object PageNumber extends OptionalQueryParamDecoderMatcher[Int]("p")

    def detailPage(
      sourceSystem: String,
      sourceSystemKey: String,
      flashMessage: Option[String] = None,
      errors: Seq[String] = Seq()
    ): F[TypedTag[String]] = {
      val page: F[TypedTag[String]] = for {
        organizations <- organization.get(Organization.Id(sourceSystem, sourceSystemKey))
        page <- organizations.traverse(org =>
          layout.Page(layout.Header, layout.DetailPage(org, flashMessage.map(layout.FlashMessage), errors)).render
        )
        combined <- page.map(_.pure) getOrElse layout.Page(layout.Header, layout.ErrorMessage("No organization found")).render
      } yield combined
      page
    }

    HttpRoutes.of[F] {

      case GET -> Root :? SearchQuery(optionalQuery) +& Field(optionalField) +& PageNumber(optionalPageNumber) =>
        val pageNumber = optionalPageNumber getOrElse 1
        val field = optionalField getOrElse "all"

        val homepage: F[TypedTag[String]] = for {
          organizations <- optionalQuery.map(q => organization.search(q, field, Page(pageNumber))) getOrElse OrganizationResult.empty
            .pure[F]
          homepage <- layout
            .Page(
              layout.Header,
              layout.SearchPage(
                searchValue = optionalQuery getOrElse "",
                selectedField = optionalField getOrElse "all",
                result = organizations
              )
            )
            .render
        } yield homepage

        for {
          homepage <- homepage.attempt
          response <- homepage match {
            case Left(error) => errorResponse(error.getMessage)
            case Right(page) => Ok(page)
          }
        } yield response

      case GET -> Root / "group" / groupId =>
        for {
          organizations <- organization.getByGroupId(groupId)
          page          <- layout.Page(layout.Header, layout.GroupPage(organizations)).render
          resp          <- Ok(page)
        } yield resp

      case req @ GET -> Root / "organization" / sourceSystem / sourceSystemKey =>
        val message = req.cookies.find(c => c.name == "flashMessage").map(_.content)
        for {
          detailpage <- detailPage(sourceSystem, sourceSystemKey, message).attempt
          response <- detailpage match {
            case Left(error) => errorResponse(error.getMessage)
            case Right(page) => Ok(page)
          }
        } yield response.addCookie(ResponseCookie("flashMessage", "", path = Some("/")).clearCookie)

      case req @ POST -> Root / "organization" / sourceSystem / sourceSystemKey / "groupId" =>
        println(s"Request for: $sourceSystem:$sourceSystemKey")

        val page: F[Unit] = for {
          params         <- req.bodyText.compile.string.map(Query.unsafeFromString(_).params)
          groupingAction <- organization.extractGroupingActionFromRequest(params)
          _              <- organization.updateGroup(Organization.Id(sourceSystem, sourceSystemKey), groupingAction)
        } yield ()

        for {
          updatepage <- page.attempt
          response <- updatepage match {
            case Left(error) =>
              for {
                detailPage <- detailPage(sourceSystem, sourceSystemKey, errors = Seq(error.getMessage))
                errorPage  <- BadRequest(detailPage)
              } yield errorPage
            case Right(_) =>
              for {
                success <- Found("", Location(Uri().withPath(Uri.Path.unsafeFromString(s"/organization/$sourceSystem/$sourceSystemKey"))))
              } yield success.addCookie(ResponseCookie("flashMessage", "GroupId successfully updated", path = Some("/")))
          }
        } yield response
    }
  }

}
