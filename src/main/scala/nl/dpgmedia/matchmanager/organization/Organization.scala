package nl.dpgmedia.matchmanager.organization

import cats.effect.Concurrent
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import org.http4s.circe.{jsonEncoderOf, jsonOf}
import org.http4s.{EntityDecoder, EntityEncoder}

case class Organization(
  groupId: String,
  originalGroupId: String,
  sourceSystem: String,
  sourceSystemKey: String,
  name: Option[String],
  cocNumber: Option[String],
  street: Option[String],
  city: Option[String]
)

object Organization {
  case class Id(sourceSystem: String, sourceSystemKey: String)

  implicit val statusDecoder: Decoder[Organization] = deriveDecoder[Organization]

  implicit def statusEntityDecoder[F[_]: Concurrent]: EntityDecoder[F, Organization] =
    jsonOf

  implicit val statusEncoder: Encoder[Organization] = deriveEncoder[Organization]

  implicit def statusEntityEncoder[F[_]]: EntityEncoder[F, Organization] =
    jsonEncoderOf

  def fieldNames: Array[String] = classOf[Organization].getDeclaredFields.map(f => f.getName)
}
