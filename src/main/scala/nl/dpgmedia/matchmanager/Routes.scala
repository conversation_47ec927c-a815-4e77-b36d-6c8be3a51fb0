package nl.dpgmedia.matchmanager

import cats.effect.Async
import org.http4s.dsl.Http4sDsl
import org.http4s.{HttpRoutes, StaticFile}

object Routes {

  def staticRoutes[F[_]: Async](): HttpRoutes[F] = {
    val dsl = new Http4sDsl[F] {}
    import dsl._

    HttpRoutes.of[F] {
      case request @ GET -> Root / "favicon.ico" =>
        StaticFile
          .fromResource("/favicon-16x16.png", Some(request))
          .getOrElseF(NotFound())

      case request @ GET -> Root / "match-manager.css" =>
        StaticFile
          .fromResource("/match-manager.css", Some(request))
          .getOrElseF(NotFound())
    }
  }

}
