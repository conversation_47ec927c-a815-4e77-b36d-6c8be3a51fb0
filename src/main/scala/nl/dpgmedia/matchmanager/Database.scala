package nl.dpgmedia.matchmanager

import cats.effect._
import doobie._
import doobie.hikari._

trait Database[F[_]] {

  def transactor(
    databaseConfiguration: Configuration.Database.type
  ): Resource[F, HikariTransactor[F]]

}

object Database {

  def apply[F[_]: Async]: Database[F] = new Database[F] {

    override def transactor(
      databaseConfiguration: Configuration.Database.type
    ): Resource[F, HikariTransactor[F]] =
      for {
        ce <- ExecutionContexts.fixedThreadPool[F](
          Runtime.getRuntime.availableProcessors()
        ) // our connect EC
        xa <- HikariTransactor.newHikariTransactor[F](
          "net.snowflake.client.jdbc.SnowflakeDriver", // driver classname
          databaseConfiguration.connectionString,
          databaseConfiguration.user,
          databaseConfiguration.password,
          ce // await connection here
        )
      } yield xa

  }

}
