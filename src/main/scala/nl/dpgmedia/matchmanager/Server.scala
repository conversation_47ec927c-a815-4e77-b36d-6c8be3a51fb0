package nl.dpgmedia.matchmanager

import cats.effect.{Async, Resource}
import cats.syntax.all._
import com.comcast.ip4s._
import doobie.util.transactor.Transactor
import fs2.Stream
import nl.dpgmedia.matchmanager.organization.{OrganizationDsl, OrganizationRepository}
import nl.dpgmedia.matchmanager.status.{ApplicationStatus, StatusRepository}
import org.http4s.ember.client.EmberClientBuilder
import org.http4s.ember.server.EmberServerBuilder
import org.http4s.implicits._
import org.http4s.server.middleware.Logger

object Server {

  def stream[F[_]: Async](transactor: Transactor[F]): Stream[F, Int] = {
    for {
      client <- Stream.resource(EmberClientBuilder.default[F].build)

      layout = new Layout[F]()
      statusAlg = ApplicationStatus.impl[F](
        StatusRepository,
        transactor
      )
      organizationAlg = OrganizationDsl.impl[F](OrganizationRepository[F](transactor))

      // Combine Service Routes into an HttpApp.
      // Can also be done via a Router if you
      // want to extract a segments not checked
      // in the underlying routes.
      httpApp = (
        Routes.staticRoutes[F]() <+>
          status.StatusRoutes(statusAlg) <+>
          organization.OrganizationRoutes(organizationAlg, layout)
      ).orNotFound

      // With Middlewares in place
      finalHttpApp = Logger.httpApp(false, false)(httpApp)

      exitCode <- Stream.resource(
        EmberServerBuilder
          .default[F]
          .withHost(ipv4"0.0.0.0")
          .withPort(port"8080")
          .withHttpApp(finalHttpApp)
          .build >>
        Resource.eval(Async[F].never >> 1.pure[F])
      )
    } yield exitCode
  }.drain

}
