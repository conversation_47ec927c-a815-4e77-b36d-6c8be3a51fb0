package nl.dpgmedia.matchmanager.status

import cats.effect.Concurrent
import io.circe.{Decoder, Encoder}
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import org.http4s.circe.{jsonEncoderOf, jsonOf}
import org.http4s.{EntityDecoder, EntityEncoder}

final case class Status(status: String)

object Status {
  implicit val statusDecoder: Decoder[Status] = deriveDecoder[Status]

  implicit def statusEntityDecoder[F[_]: Concurrent]: EntityDecoder[F, Status] =
    jsonOf

  implicit val statusEncoder: Encoder[Status] = deriveEncoder[Status]

  implicit def statusEntityEncoder[F[_]]: EntityEncoder[F, Status] =
    jsonEncoderOf

}
