package nl.dpgmedia.matchmanager.status

import cats.effect._
import cats.syntax.all._
import org.http4s.HttpRoutes
import org.http4s.dsl.Http4sDsl

object StatusRoutes {

  def apply[F[_]: Async](
    applicationStatus: ApplicationStatus[F]
  ): HttpRoutes[F] = {
    val dsl = new Http4sDsl[F] {}
    import dsl._

    HttpRoutes.of[F] { case GET -> Root / "status" =>
      for {
        status <- applicationStatus.get
        resp   <- Ok(status)
      } yield resp
    }
  }

}
