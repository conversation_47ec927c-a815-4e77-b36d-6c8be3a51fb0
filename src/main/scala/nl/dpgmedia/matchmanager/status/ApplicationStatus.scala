package nl.dpgmedia.matchmanager.status

import cats.effect._
import cats.syntax.all._
import doobie.Transactor
import doobie.implicits._

trait ApplicationStatus[F[_]] {
  def get: F[Status]
}

object ApplicationStatus {
  def apply[F[_]](implicit ev: ApplicationStatus[F]): ApplicationStatus[F] = ev

  def impl[F[_]: Async](
    repository: StatusRepository,
    transactor: Transactor[F]
  ): ApplicationStatus[F] = new ApplicationStatus[F] {

    override def get: F[Status] =
      for {
        status <- repository.getStatus.transact(transactor).attempt
      } yield status.leftMap(e => Status(s"orange: ${e.getMessage}")).merge

  }

}
