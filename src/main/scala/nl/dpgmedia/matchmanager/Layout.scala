package nl.dpgmedia.matchmanager

import _root_.scalatags.Text.TypedTag
import _root_.scalatags.Text.all._
import _root_.scalatags.Text.tags2.{section, title => pageTitle}
import cats.MonadThrow
import cats.syntax.applicative._
import cats.syntax.applicativeError._
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.traverse._
import nl.dpgmedia.matchmanager.organization.{AttachToExisting, DetachFromGroup, Organization, OrganizationResult, Pagination}

class Layout[F[_]: MonadThrow] {

  trait PageElement {
    def render: F[TypedTag[String]]
  }

  trait Body extends PageElement

  case object EmptyBody extends Body {
    override def render: F[TypedTag[String]] = section().pure
  }

  trait Header extends PageElement

  case object Header extends Header {

    override def render: F[TypedTag[String]] = header(
      cls := "header",
      h1(a(href := "/", "Match Manager"))
    ).pure

  }

  case class Page(pageHeader: Header, pageBody: Body) extends PageElement {

    override def render: F[TypedTag[String]] = for {
      renderedHeader <- pageHeader.render
      renderedBody   <- pageBody.render
    } yield html(
      head(
        link(
          rel := "stylesheet",
          href := "/match-manager.css"
        ),
        link(
          rel := "icon",
          href := "/favicon.ico",
          `type` := "image/png"
        ),
        pageTitle(s"Match Manager")
      ),
      body(
        renderedHeader,
        renderedBody
      )
    )

  }

  case class ErrorMessage(msg: String) extends Body {
    override def render: F[TypedTag[String]] = section(h2(msg)).pure
  }

  case class SearchPage(searchValue: String, selectedField: String = "all", result: OrganizationResult) extends Body {

    override def render: F[TypedTag[String]] =
      for {
        table <- result.results.headOption.traverse(_ => OrganizationTable(result.results, PaginationFooter(result.pagination)).render)
      } yield section(
        form(
          id := "search",
          select(
            name := "f",
            value := selectedField,
            for { field <- "all" +: Organization.fieldNames.toSeq } yield
              if (selectedField == field) {
                option(value := field, selected := "selected", field)
              } else {
                option(value := field, field)
              }
          ),
          input(name := "q", value := searchValue, `type` := "text", placeholder := "search"),
          button("Go")
        ),
        table getOrElse ""
      )

  }

  case class OrganizationRow(organization: Organization) extends PageElement {

    override def render: F[TypedTag[String]] = {
      def detailLink(text: String): TypedTag[String] =
        a(href := s"/organization/${organization.sourceSystem}/${organization.sourceSystemKey}", text)

      def groupLink(text: String): TypedTag[String] =
        a(href := s"/group/$text", text)

      def link(field: String, value: String): TypedTag[String] = field match {
        case "groupId"         => td(cls := "group", groupLink(value))
        case "originalGroupId" => td(cls := "group", groupLink(value))
        case _                 => td(detailLink(value))
      }

      tr(organization.productIterator.toList.zip(Organization.fieldNames).map {
        case (Some(v), field) => link(field, v.toString)
        case (None, field)    => link(field, "-")
        case (v, field)       => link(field, v.toString)
      }).pure
    }

  }

  case object OrganizationHeader extends PageElement {

    override def render: F[TypedTag[String]] =
      tr(Organization.fieldNames.map(f => th(f))).pure

  }

  trait OrganizationFooter extends PageElement

  case class OrganizationControlsFooter(sourceSysyem: String, sourceSystemKey: String, errors: Seq[String]) extends OrganizationFooter {

    override def render: F[TypedTag[String]] = tr(
      td(
        colspan := Organization.fieldNames.length,
        if (errors.nonEmpty) ul(errors.map(e => li(e)))
        else "",
        form(
          action := s"/organization/$sourceSysyem/$sourceSystemKey/groupId",
          method := "post",
          button(name := "action", value := DetachFromGroup.key, "detach from current group")
        ),
        form(
          action := s"/organization/$sourceSysyem/$sourceSystemKey/groupId",
          method := "post",
          input(name := "groupId", `type` := "text", placeholder := "target groupId"),
          button(name := "action", value := AttachToExisting.key, "add to existing group")
        )
      )
    ).pure

  }

  case class PaginationFooter(pagination: Pagination) extends OrganizationFooter {

    override def render: F[TypedTag[String]] =
      tr(
        td(
          colspan := Organization.fieldNames.length,
          span(
            span(
              cls := "navigation",
              pagination.previousPage.map(page =>
                button(
                  name := "p",
                  formA := "search",
                  value := page.pageNumber.toString,
                  "previous"
                )
              ),
              pagination.nextPage.map(page =>
                button(
                  name := "p",
                  formA := "search",
                  value := page.pageNumber.toString,
                  "next"
                )
              )
            ),
            s"Page ${pagination.page.pageNumber} of ${pagination.lastPage.pageNumber} (${pagination.total} ${if (pagination.total == 1) "result"
            else "results"})"
          )
        )
      ).pure

  }

  case class OrganizationTable(organizations: List[Organization], organizationFooter: OrganizationFooter) extends PageElement {

    override def render: F[TypedTag[String]] = for {
      header <- organizations.headOption.traverse(_ => OrganizationHeader.render)
      rows   <- organizations.traverse(r => OrganizationRow(r).render)
      footer <- organizationFooter.render
      table <- header.map { h =>
        table(
          thead(h),
          tbody(rows),
          tfoot(footer)
        ).pure
      } getOrElse new Throwable("No items").raiseError
    } yield table

  }

  case class GroupPage(result: OrganizationResult) extends Body {

    override def render: F[TypedTag[String]] = for {
      organizationTable <- result.results.headOption.traverse(_ =>
        OrganizationTable(result.results, PaginationFooter(result.pagination)).render
      )
      groupPage <- result.results.headOption.map { _ =>
        section(
          organizationTable
        ).pure
      } getOrElse section(
        h2("No organizations found")
      ).pure

    } yield groupPage

  }

  case class FlashMessage(message: String) extends PageElement {
    override def render: F[TypedTag[String]] = p(cls := "flash-message", message).pure
  }

  case class DetailPage(organization: Organization, flashMessage: Option[FlashMessage], errors: Seq[String]) extends Body {

    override def render: F[TypedTag[String]] = for {
      message <- flashMessage.traverse(_.render)
      organizationTable <- OrganizationTable(
        List(organization),
        OrganizationControlsFooter(organization.sourceSystem, organization.sourceSystemKey, errors)
      ).render
    } yield section(
      message,
      organizationTable
    )

  }

}
