2025-07-30 10:56:34.912872+02:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: The database has been closed; SQL statement:
select selected_server from chosen_build_server where md5 = ?; [90098-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.engine.SessionLocal.getDatabase(SessionLocal.java:672)
	at org.h2.command.Command.getDatabase(Command.java:434)
	at org.h2.command.Command.<init>(Command.java:62)
	at org.h2.command.CommandContainer.<init>(CommandContainer.java:83)
	at org.h2.command.Parser.prepareCommand(Parser.java:494)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:315)
	at scala.meta.internal.metals.JdbcEnrichments$XtensionConnection.query(JdbcEnrichments.scala:35)
	at scala.meta.internal.metals.ChosenBuildServers.selectedServer(ChosenBuildServers.scala:17)
	at scala.meta.internal.metals.ProjectMetalsLspService.$anonfun$buildTools$2(ProjectMetalsLspService.scala:92)
	at scala.meta.internal.builds.BuildTools.isAutoConnectable(BuildTools.scala:58)
	at scala.meta.internal.metals.ConnectionProvider$Connect$.$anonfun$bloopInstallAndConnect$1(ConnectionProvider.scala:681)
	at scala.meta.internal.metals.Interruptable.$anonfun$flatMap$1(Interruptable.scala:33)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:470)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
