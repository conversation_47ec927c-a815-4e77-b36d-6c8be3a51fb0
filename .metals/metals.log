2025.07.30 10:56:33 INFO  Started: Metals version 1.6.0 in folders '/home/<USER>/projects/match-manager' for client Visual Studio Code 1.102.3.
2025.07.30 10:56:34 INFO  no build target found for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.07.30 10:56:33 INFO  Shutting down server
2025.07.30 10:56:33 INFO  shutting down Metals
Jul 30, 2025 10:56:34 AM io.undertow.Undertow start
INFO: starting server: Undertow - 2.3.12.Final
Jul 30, 2025 10:56:34 AM org.xnio.Xnio <clinit>
INFO: XNIO version 3.8.16.Final
Jul 30, 2025 10:56:34 AM org.xnio.nio.NioXnio <clinit>
INFO: XNIO NIO Implementation Version 3.8.16.Final
2025.07.30 10:56:34 INFO  running '/usr/bin/sbt -Dbloop.export-jar-classifiers=sources bloopInstall'
Jul 30, 2025 10:56:34 AM org.jboss.threads.Version <clinit>
INFO: JBoss Threads version 3.5.0.Final
2025.07.30 10:56:34 INFO  Metals MCP server started on port: 46175.
2025.07.30 10:56:34 INFO  time: ran 'sbt bloopInstall' in 0.19s
2025.07.30 10:56:34 ERROR sbt command failed: /usr/bin/sbt -Dbloop.export-jar-classifiers=sources bloopInstall
2025.07.30 10:56:34 INFO  Exiting server
2025.07.30 10:56:34 ERROR Unexpected error initializing server: 
org.h2.jdbc.JdbcSQLNonTransientConnectionException: The database has been closed; SQL statement:
select selected_server from chosen_build_server where md5 = ?; [90098-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.engine.SessionLocal.getDatabase(SessionLocal.java:672)
	at org.h2.command.Command.getDatabase(Command.java:434)
	at org.h2.command.Command.<init>(Command.java:62)
	at org.h2.command.CommandContainer.<init>(CommandContainer.java:83)
	at org.h2.command.Parser.prepareCommand(Parser.java:494)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:315)
	at scala.meta.internal.metals.JdbcEnrichments$XtensionConnection.query(JdbcEnrichments.scala:35)
	at scala.meta.internal.metals.ChosenBuildServers.selectedServer(ChosenBuildServers.scala:17)
	at scala.meta.internal.metals.ProjectMetalsLspService.$anonfun$buildTools$2(ProjectMetalsLspService.scala:92)
	at scala.meta.internal.builds.BuildTools.isAutoConnectable(BuildTools.scala:58)
	at scala.meta.internal.metals.ConnectionProvider$Connect$.$anonfun$bloopInstallAndConnect$1(ConnectionProvider.scala:681)
	at scala.meta.internal.metals.Interruptable.$anonfun$flatMap$1(Interruptable.scala:33)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:470)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.lang.Thread.run(Thread.java:1583)

2025.07.30 10:56:34 WARN  sql closed: insert into sbt_digest values (?, ?, ?);
2025.07.30 10:56:40 INFO  Started: Metals version 1.6.0 in folders '/home/<USER>/projects/match-manager' for client Visual Studio Code 1.102.3.
2025.07.30 10:56:41 INFO  no build target found for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.07.30 10:56:41 INFO  skipping build import with status 'Started'
2025.07.30 10:56:41 INFO  no build target found for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.07.30 10:56:41 WARN  no build target for: /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala
Jul 30, 2025 10:56:41 AM io.undertow.Undertow start
INFO: starting server: Undertow - 2.3.12.Final
Jul 30, 2025 10:56:41 AM org.xnio.Xnio <clinit>
INFO: XNIO version 3.8.16.Final
Jul 30, 2025 10:56:41 AM org.xnio.nio.NioXnio <clinit>
INFO: XNIO NIO Implementation Version 3.8.16.Final
Jul 30, 2025 10:56:41 AM org.jboss.threads.Version <clinit>
INFO: JBoss Threads version 3.5.0.Final
2025.07.30 10:56:41 INFO  Metals MCP server started on port: 46175.
2025.07.30 10:56:42 INFO  no build target found for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.07.30 10:56:42 INFO  no build target found for /home/<USER>/projects/match-manager/src/main/scala/nl/dpgmedia/matchmanager/Database.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.07.30 10:56:43 INFO  restarted 1 presentation compiler
