pipeline {
    agent {
        node {
            label 'ndp'
        }
    }
    environment {
        String SLACK_CHANNEL = '#dpgr_alpakka_notifications'
        String SLACK_MESSAGE = "@here Build #${env.BUILD_NUMBER} for ${env.JOB_NAME} (<${env.BUILD_URL}|open>) has failed"
        String SLACK_TEAM_DOMAIN = 'dpos'
    }
    options {
        ansiColor('xterm')
        buildDiscarder(logRotator(numToKeepStr: '25'))
        disableConcurrentBuilds()
        disableResume()
    }
    stages {
        stage('Compile') {
            steps {
                ansiColor('xterm') {
                    sh "sbt -Drevision=${env.BUILD_NUMBER} clean scalafmtCheckAll"
                }
            }
        }
        stage('Test') {
            steps {
                ansiColor('xterm') {
                    sh "sbt test"
                }
            }
        }
        stage('Publish') {
            steps {
                ansiColor('xterm') {
                    sh "aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 674201978047.dkr.ecr.eu-west-1.amazonaws.com"
                    sh "sbt -Drevision=${env.BUILD_NUMBER} docker:publish"
                }
            }
        }
        stage('Deploy acceptance') {
            steps {
                ansiColor('xterm') {
                    sh "sbt \"deploy acc default ${env.BUILD_NUMBER}\""
                }
            }
        }
        stage('Deploy production') {
            steps {
                ansiColor('xterm') {
                    sh "sbt \"deploy pro default ${env.BUILD_NUMBER}\""
                }
            }
        }
    }
    post {
        unsuccessful {
            withCredentials([string(credentialsId: 'SLACK_TOKEN', variable: 'SLACK_TOKEN')]) {
                slack(SLACK_CHANNEL, '#c11238', SLACK_MESSAGE, SLACK_TEAM_DOMAIN, SLACK_TOKEN)
            }
        }
    }
}

def slack(String channel, String color, String message, String teamDomain, String token) {
    slackSend channel: channel, color: color, message: message, teamDomain: teamDomain, token: token
}