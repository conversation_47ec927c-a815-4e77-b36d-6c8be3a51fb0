version = 3.1.2

runner.dialect = scala213source3

style = defaultWithAlign

align.openParenCallSite = false
align.openParenDefnSite = false
align.tokens = [{code = "->"}, {code = "<-"}, {code = "=>", owner = "Case"}, {code = "%", owner = "Term.ApplyInfix"}, {code = "%%", owner = "Term.ApplyInfix"}]
align.preset=more
continuationIndent.callSite = 2
continuationIndent.defnSite = 2
danglingParentheses.exclude = []
indentOperator.preset = spray
maxColumn = 140
newlines.topLevelStatementBlankLines = [ { blanks = 1 } ]
project.excludeFilters = [".*\\.sbt", ".*\\.scala\\.html"]
rewrite.redundantBraces.stringInterpolation = true
rewrite.rules = [RedundantParens, RedundantBraces, SortImports]
rewrite.sortModifiers.order = ["implicit", "final", "sealed", "abstract","override", "private", "protected", "lazy"]
spaces.inImportCurlyBraces = false
unindentTopLevelOperators = true

rewrite.trailingCommas.style = never