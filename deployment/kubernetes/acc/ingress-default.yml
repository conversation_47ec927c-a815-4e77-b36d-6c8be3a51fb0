apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: match-manager
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx-internal
  rules:
  - host: match-manager-acc.persgroep.digital
    http:
      paths:
      - path:
        pathType: ImplementationSpecific
        backend:
          service:
            name: match-manager
            port:
              number: 8080
