val Http4sVersion = "1.0.0-M29"
val MunitVersion = "0.7.29"
val LogbackVersion = "1.2.8"
val MunitCatsEffectVersion = "1.0.5"
val doobieVersion = "1.0.0-RC1"
val CirceVersion = "0.15.0-M1"
val doobieQuillVersion = "0.0.2"
val quillVersion = "3.11.0"
val scalatestVersion = "3.2.10"

val revision = Option(System.getProperty("revision")).getOrElse("1")

lazy val root = (project in file("."))
  .enablePlugins(JavaAppPackaging)
  .settings(
    organization := "nl.dpgmedia",
    name := "match-manager",
    scalaVersion := "2.13.9",
    version := revision,
    libraryDependencies ++= Seq(
      "org.http4s"      %% "http4s-ember-server"           % Http4sVersion,
      "org.http4s"      %% "http4s-ember-client"           % Http4sVersion,
      "org.http4s"      %% "http4s-circe"                  % Http4sVersion,
      "org.http4s"      %% "http4s-dsl"                    % Http4sVersion,
      "org.scalameta"   %% "munit"                         % MunitVersion           % Test,
      "org.typelevel"   %% "munit-cats-effect-3"           % MunitCatsEffectVersion % Test,
      "org.typelevel"   %% "cats-effect-testing-scalatest" % "1.4.0"                % Test,
      "ch.qos.logback"   % "logback-classic"               % LogbackVersion,
      "com.lihaoyi"      % "scalatags_2.13"                % "0.10.0",
      "org.ekrich"      %% "sconfig"                       % "1.4.5",
      "org.tpolecat"    %% "doobie-core"                   % doobieVersion,
      "org.tpolecat"    %% "doobie-hikari"                 % doobieVersion,
      "net.snowflake"    % "snowflake-jdbc"                % "3.13.11",
      "io.circe"        %% "circe-generic"                 % CirceVersion,
      "io.getquill"     %% "quill-jdbc"                    % quillVersion,
      "io.getquill"     %% "quill-core"                    % quillVersion,
      "org.polyvariant" %% "doobie-quill"                  % doobieQuillVersion,
      "mysql"            % "mysql-connector-java"          % "8.0.25",
      "org.scalatest"   %% "scalatest"                     % scalatestVersion       % Test
    ),
    testFrameworks += new TestFramework("munit.Framework"),
    bashScriptExtraDefines ++= javaOptions.value map (opt => s"""addJava "--add-opens=java.base/jdk.internal.misc=ALL-UNNAMED"""")
  )
