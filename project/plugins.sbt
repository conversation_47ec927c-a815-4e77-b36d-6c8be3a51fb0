addSbtPlugin("com.lightbend.sbt" % "sbt-javaagent" % "0.1.4") // ALPN agent, only required on JVM 8

// sbt plugin to load environment variables from .env into the JVM System Environment for local development.
// see also: https://github.com/mefellows/sbt-dotenv
addSbtPlugin("au.com.onegeek" %% "sbt-dotenv" % "2.1.146")

// SBT native packager lets you build application packages in native formats.
// see also: https://github.com/sbt/sbt-native-packager
addSbtPlugin("com.typesafe.sbt" % "sbt-native-packager" % "1.7.2")

// enabling a super-fast development turnaround for your Scala applications.
// features:
// - Starting and stopping your application in the background of your interactive SBT shell (in a forked JVM)
// - Triggered restart: automatically restart your application as soon as some of its sources have been changed
// see also: https://github.com/spray/sbt-revolver
addSbtPlugin("io.spray" % "sbt-revolver" % "0.9.1")

// a plugin for SBT that integrates the scoverage code coverage library.
// see also: https://github.com/scoverage/sbt-scoverage
addSbtPlugin("org.scoverage" % "sbt-scoverage" % "1.6.1")

addSbtPlugin("org.scalameta" % "sbt-scalafmt" % "2.4.4")

// a list of recommended scalac options
// see also: https://github.com/DavidGregory084/sbt-tpolecat
addSbtPlugin("io.github.davidgregory084" % "sbt-tpolecat" % "0.1.20")

addDependencyTreePlugin